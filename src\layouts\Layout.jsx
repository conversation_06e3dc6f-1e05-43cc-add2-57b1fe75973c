import React, { useState, createContext, useContext } from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import '../styles/Layout.css';

// Create context for sidebar state
const SidebarContext = createContext();

// Custom hook to use sidebar context
export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

const Layout = () => {
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(false);

  const handleSidebarToggle = (expanded) => {
    setIsSidebarExpanded(expanded);
  };

  return (
    <SidebarContext.Provider value={{ isSidebarExpanded }}>
      <div className="flex h-screen bg-[#F8FAFC] overflow-hidden">
        <Sidebar onToggle={handleSidebarToggle} />
        <main
          className={`
            flex-1 overflow-auto p-6
            transition-all duration-600 ease-in-out
            ${isSidebarExpanded ? 'ml-[325px]' : 'ml-[54px]'}
          `}
        >
          <div className="container mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </SidebarContext.Provider>
  );
};

export default Layout;
