import { useState } from "react";
import SearchIcon from "../assets/icons/SearchIcon.svg";
import downArrow from "../assets/icons/downArrow.svg";
import SearchField from "./forms/SearchField";

const SearchComponent = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleInputChange = (e) => {
    setSearchValue(e.target.value);
  };

  const handleSearch = () => {
    console.log("Searching for:", searchValue);
    // Add your search logic here
  };

  return (
    <div className="flex flex-col gap-2 pb-3">
      <div
        className="flex items-center p-[8px] w-[318px] rounded-xl border border-[rgba(18,18,18,0.08)] bg-[#EEF4FA] cursor-pointer"
        onClick={handleToggle}
      >
        <div className="flex items-center gap-2">
          <img src={SearchIcon} alt="search" className="w-5 h-5" />
          <span className="text-[#042174] text-base font-medium">Search</span>
        </div>
        <div className="ml-auto">
          <img
            src={downArrow}
            alt="expand"
            className={`w-5 h-5 transition-transform duration-200 ${
              isExpanded ? "rotate-180" : ""
            }`}
          />
        </div>
      </div>

      {isExpanded && (
        <div className="flex gap-2 w-[318px] rounded-xl border border-[#E1E1E2] bg-white p-2">
          <div className="flex-1">
            <SearchField
              placeholder="Search Word"
              value={searchValue}
              onChange={handleInputChange}
              icon={SearchIcon}
              inputClassName="bg-white text-[#64748B]"
              containerClassName="w-full"
            />
          </div>
          <button
            onClick={handleSearch}
            className="px-6 py-[6px] bg-[#042174] text-white text-sm font-bold rounded-xl hover:bg-[#001845] transition-colors text-center cursor-pointer"
          >
            Search
          </button>
        </div>
      )}
    </div>
  );
};

export default SearchComponent;
