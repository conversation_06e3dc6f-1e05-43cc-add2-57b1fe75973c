import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

// Cross icon component
const CrossIcon = ({ className }) => (
  <svg 
    width="20" 
    height="20" 
    viewBox="0 0 20 20" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path 
      d="M15 5L5 15M5 5L15 15" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round"
    />
  </svg>
);

const ImageUpload = ({ onFileUpload, label, labelClassName = '' }) => {
  const [uploadedFiles, setUploadedFiles] = useState([]);

  const onDrop = useCallback((acceptedFiles) => {
    // Check if adding new files would exceed the limit of 4
    if (uploadedFiles.length + acceptedFiles.length > 4) {
      alert('Maximum 4 images allowed');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      file,
      preview: URL.createObjectURL(file)
    }));
    setUploadedFiles(prev => [...prev, ...newFiles]);
    if (onFileUpload) {
      onFileUpload(acceptedFiles);
    }
  }, [onFileUpload, uploadedFiles.length]);

  const removeFile = (indexToRemove) => {
    setUploadedFiles(prev => {
      const newFiles = prev.filter((_, index) => index !== indexToRemove);
      if (onFileUpload) {
        onFileUpload(newFiles.map(f => f.file));
      }
      return newFiles;
    });
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpeg', '.jpg'],
      'image/png': ['.png']
    },
    maxSize: 6 * 1024 * 1024, // 6MB
  });

  // Cleanup previews when component unmounts
  React.useEffect(() => {
    return () => {
      uploadedFiles.forEach(file => {
        URL.revokeObjectURL(file.preview);
      });
    };
  }, [uploadedFiles]);

  return (
    <div className="w-full">
      {label && (
        <label className={`block mb-2 ${labelClassName}`}>
          {label}
        </label>
      )}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-[6px] p-6 text-center cursor-pointer transition-all duration-200 ease-in-out ${
          isDragActive 
          ? 'border-[#042174] bg-[#E6EBFF] scale-[1.01] shadow-lg' 
          : 'border-[#042174] border-opacity-50 bg-[#F2F2F2] hover:border-opacity-100 hover:bg-[#F5F7FF] hover:scale-[1.002]'
        }`}
      >
        <input {...getInputProps()} />
        {isDragActive ? (
          <div className="py-4">
            <p className="text-lg text-[#042174] font-semibold">Drop your images here!</p>
            <p className="text-sm text-[#4A63C6] mt-1">Release to upload</p>
          </div>
        ) : (
          <div className="space-y-2">
            <p className="text-base text-[#042174] font-semibold">
              Drag & drop files or <span className="text-[#042174] underline font-semibold">Browse</span>
            </p>
            <p className="text-[10px] text-[#676767] ">
              Supported formats: jpeg, jpg, png
            </p>
          </div>
        )}
      </div>
      <p className="text-[10px] text-[#676767]  pt-1 pl-1">
        *uploadable file size: 6MB
      </p>

      {/* Image previews */}
      {uploadedFiles.length > 0 && (
        <div className="mt-2 grid grid-cols-3 gap-4">
          {uploadedFiles.map((file, index) => (
            <div 
              key={file.preview} 
              className="relative group rounded-lg border border-gray-200"
            >
              <img
                src={file.preview}
                alt={`preview ${index + 1}`}
                className="w-full h-20 object-cover"
              />
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  removeFile(index);
                }}
                className="absolute -top-2 -right-2 p-1 bg-white rounded-full hover:bg-gray-100 shadow-sm"
              >
                <CrossIcon className="w-3 h-3 text-gray-600" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
