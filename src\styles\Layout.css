.app-container {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex-grow: 1;
  margin-left: 280px;
  padding: 20px;
  transition: margin-left 0.3s ease;
  background-color: #f5f5f5;
}

.main-content.sidebar-collapsed {
  margin-left: 70px;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 70px;
  }
}

/* Custom drawer animation to match sidebar easing */
.drawer {
  transition: transform 600ms ease-in-out !important;
}

.drawer .rmd-overlay {
  transition: opacity 600ms ease-in-out !important;
}
