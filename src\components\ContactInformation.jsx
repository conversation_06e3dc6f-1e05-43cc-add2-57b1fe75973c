import React from 'react'
import EmailIcon from '../assets/icons/Email.svg'
import PhoneIcon from '../assets/icons/Call.svg'
import ringingIcon from '../assets/icons/ringingIcon.svg'
import locationIcon from '../assets/icons/Location.svg'

const ContactInformation = () => {
  return (
    <div className='flex flex-col items-start w-[326px] p-[18px_16px] gap-[10px]  bg-white'>
        <span className='text-black font-medium'>Contact Information</span>
        
        {/* Michilog hotline section */}
        <div className='flex items-start gap-2'>
          <img src={PhoneIcon} alt="phone" className='w-5 h-5' />
          <div className='flex flex-col'>
            <span className='text-[#626262] text-sm  pb-1'>
              Michilog dedicated hotline
            </span>
            <span className='text-black text-xs '>
              Toll-free number: 0120-088-042
            </span>
          </div>
        </div>

        {/* IP/PHS section */}
        <div className='flex items-start gap-2'>
          <img src={ringingIcon} alt="phone" className='w-5 h-5' />
          <div className='flex flex-col'>
            <span className='text-[#626262] text-sm  pb-1'>
              For IP/PHS
            </span>
            <span className='text-black text-xs '>
              050-2018-6521
            </span>
          </div>
        </div>

        {/* Email section */}
        <div className='flex items-start gap-2'>
          <img src={EmailIcon} alt="email" className='w-5 h-5' />
          <div className='flex flex-col'>
            <span className='text-[#626262] text-sm  pb-1'>
              Email
            </span>
            <span className='text-black text-xs '>
              <EMAIL>
            </span>
          </div>
        </div>

        {/* Divider */}
        <div className='w-full h-[1px] bg-[#D0D5DD]'></div>

        {/* License Buttons */}
        <div className='flex w-full gap-2 pt-2'>
          <button className='flex whitespace-nowrap h-[38px] px-3 py-2 justify-between items-center rounded-lg border border-[#1265AE] bg-white text-[#114F85] text-sm font-medium gap-2 cursor-pointer'>
            <img src={locationIcon} alt="location" className='w-5 h-5' />
            Map License
          </button>
          <button className='flex whitespace-nowrap h-[38px] px-3 py-2 justify-between items-center rounded-lg border border-[#1265AE] bg-white text-[#114F85] text-sm font-medium gap-2 cursor-pointer'>
            <img src={locationIcon} alt="location" className='w-5 h-5' />
            OSS License
          </button>
        </div>
    </div>
  )
}

export default ContactInformation