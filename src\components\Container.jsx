import React from 'react';
import "../styles/Container.css"


const Container = ({ children, title, className, headerClassName, bodyClassName }) => {
  return (
    <div 
      className={`aisin-container ${className || ''}`}
      style={{ 
        borderRadius: 'var(--aisin-container-border-radius)',
        padding: 0, // No padding on container to allow header to stretch
        margin: 'var(--aisin-container-margin)',
        backgroundColor: 'var(--aisin-container-background-color)',
        border: 'var(--aisin-container-border)',
        overflow: 'hidden', // Ensures content doesn't overflow rounded corners
        width: '100%',  
        boxSizing: 'border-box',
      }}
    >
      {title && (
        <div 
          className={`aisin-container-header ${headerClassName || ''}`}
          style={{
            backgroundColor: 'var(--aisin-blue-primary)',
            padding: 'var(--aisin-container-padding)',
            borderTopLeftRadius: 'var(--aisin-container-border-radius)',
            borderTopRightRadius: 'var(--aisin-container-border-radius)',
          }}
        >
          {title}
        </div>
      )}
      <div 
        className={`aisin-container-body ${bodyClassName || ''}`}
        style={{
          padding: 'var(--aisin-container-padding)',
        }}
      >
        {children}
      </div>
    </div>
  );
};


export default Container;