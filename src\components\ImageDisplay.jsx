import React, { useState } from 'react'
import imageIcon from '../assets/icons/Image.svg'

const ImageDisplay = () => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)

  // Mock images data - replace with API data later
  const [images] = useState([
    {
      id: 1,
      url: 'https://picsum.photos/800/400?random=1',
      thumbnail: 'https://picsum.photos/100/80?random=1',
      timestamp: '13/04/2025, 10:30:00AM',
      category: 'Before Work'
    },
    {
      id: 2,
      url: 'https://picsum.photos/800/400?random=2',
      thumbnail: 'https://picsum.photos/100/80?random=2',
      timestamp: '13/04/2025, 11:15:30AM',
      category: 'During Work'
    },
    {
      id: 3,
      url: 'https://picsum.photos/800/400?random=3',
      thumbnail: 'https://picsum.photos/100/80?random=3',
      timestamp: '13/04/2025, 02:45:15PM',
      category: 'After Work'
    },
    {
      id: 4,
      url: 'https://picsum.photos/800/400?random=4',
      thumbnail: 'https://picsum.photos/100/80?random=4',
      timestamp: '13/04/2025, 03:20:45PM',
      category: 'Final Check'
    }
  ])

  const handleImageSelect = (index) => {
    setSelectedImageIndex(index)
  }

  const currentImage = images[selectedImageIndex]

  return (
    <div className='border border-[#E5E7EB] rounded-xl mt-3'>
      <div className='p-[14px] bg-[#042174] gap-2 flex items-center rounded-t-xl '>
        <img src={imageIcon} className='w-5 h-5' alt='image'/>
        <p className='text-[16px] font-medium text-white'>Site Documentation Photos</p>
      </div>

      <div className='flex justify-between items-center p-3'>
        <p className='text-[12px] text-black font-medium'>Before Work</p>
        <span className='text-black font-medium text-[12px]'>
          {selectedImageIndex + 1} <span className='text-[#878787] font-normal'>of {images.length} photos</span>
        </span>
      </div>

      {/* Main Image Display */}
      <div className='relative mx-3 mb-4'>
        <div className='relative overflow-hidden rounded-lg bg-gray-100'>
          <img
            src={currentImage?.url}
            alt={`Site photo ${selectedImageIndex + 1}`}
            className='w-full h-[189px] object-cover'
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/800x400/cccccc/666666?text=Image+Not+Available'
            }}
          />

          {/* Timestamp overlay */}
          <div className='absolute bottom-1 left-3 bg-blck bg-opacity-60 text-white px-2 py-1 rounded text-xs flex items-center gap-1'>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"/>
            </svg>
            {currentImage?.timestamp}
          </div>
        </div>
      </div>

      {/* Thumbnail Navigation */}
      <div className='px-3 pb-4'>
        <div className='flex gap-2 overflow-x-auto scrollbar-hide'>
          {images.map((image, index) => (
            <div
              key={image.id}
              className={`flex-shrink-0 cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                selectedImageIndex === index
                  ? 'border-[#042174] shadow-md'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onClick={() => handleImageSelect(index)}
            >
              <img
                src={image.thumbnail}
                alt={`Thumbnail ${index + 1}`}
                className='w-[53px] h-[53px] object-cover'
                onError={(e) => {
                  e.target.src = 'https://via.placeholder.com/100x80/cccccc/666666?text=No+Image'
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ImageDisplay