import React from 'react';

const InputField = ({
  label,
  name,
  type = 'text',
  placeholder,
  error,
  value,
  onChange,
  className = '',
  labelClassName = '',
  inputWrapperClassName = '',
  required = false,
  disabled = false,
  ...props
}) => {
  return (
    <div className={`form-field ${inputWrapperClassName}`}>
      {label && (
        <label 
          htmlFor={name} 
          className={labelClassName}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <input
        type={type}
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
        {...props}
      />
      {error && (
        <p className="mt-1 text-xs text-red-500">
          {error}
        </p>
      )}
    </div>
  );
};

export default InputField;
