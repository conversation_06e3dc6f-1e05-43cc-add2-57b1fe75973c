import React from 'react';

const SearchField = ({
  placeholder = '',
  value,
  onChange,
  disabled = false,
  icon,
  containerClassName = '',
  inputClassName = '',
  iconClassName = '',
  type = 'text',
  name,
  id,
  required = false,
  ...props
}) => {
  return (
    <div className={`relative flex items-center w-full ${containerClassName}`}>
      {icon && (
        <img 
          src={icon} 
          alt="input icon" 
          className={`absolute left-3 w-4 h-4 pointer-events-none ${iconClassName}`}
        />
      )}
      <input
        type={type}
        className={`w-full ${icon ? 'pl-10' : 'pl-3'} pr-3 py-[6px] rounded-xl border border-[rgba(18,18,18,0.08)] bg-white focus:outline-none ${inputClassName}`}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        name={name}
        id={id}
        required={required}
        {...props}
      />
    </div>
  );
};

export default SearchField;
