import { useState } from 'react';
import downArrow from '../assets/icons/downArrow.svg';
import alertIcon from '../assets/icons/alertIcon.svg';
import { InputField, SelectField, DatePickerField } from '../components/forms';
import ImageUpload from '../components/forms/ImageUpload';

const NewAnomalyRegistration = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [formData, setFormData] = useState({
    incidentType: '',
    address: '',
    reportedDate: '',
    images: []
  });

  const incidentTypes = [
    { value: 'type1', label: 'Type 1' },
    { value: 'type2', label: 'Type 2' },
    { value: 'type3', label: 'Type 3' },
  ];

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileUpload = (files) => {
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...files]
    }));
  };

  

  return (
    <div className="flex flex-col gap-2 mb-3">
      <div
        className="flex items-center p-[8px] w-[318px] rounded-xl border border-[rgba(18,18,18,0.08)] bg-[#EEF4FA] cursor-pointer"
        onClick={handleToggle}
      >
        <div className="flex items-center gap-2">
          <img src={alertIcon} alt="alert" className="w-5 h-5" />
          <span className="text-[#042174] text-base font-medium">New Anomaly Registration</span>
        </div>
        <div className="ml-auto">
          <img
            src={downArrow}
            alt="expand"
            className={`w-5 h-5 transition-transform duration-200 ${
              isExpanded ? "rotate-180" : ""
            }`}
          />
        </div>
      </div>

      {isExpanded && (
        <div className="flex flex-col gap-4 w-[318px] rounded-xl border border-[#E1E1E2] bg-white p-4">
          <SelectField
            label="Incident Type"
            labelClassName="text-[#131313] text-base "
            options={incidentTypes}
            value={formData.incidentType}
            onChange={(value) => handleChange('incidentType', value)}
            placeholder="Please Select Incident Type"
          />
          
          <div className="space-y-1">
            <ImageUpload 
              label="Image Upload"
              labelClassName="text-[#131313] text-base "
              onFileUpload={handleFileUpload} 
            />
          </div>          
          <InputField
            label="Address/ Coordinates"
            labelClassName="text-[#131313] text-base  block mb-2 text-base"
            placeholder="Please Enter Address or Coordinates"
            value={formData.address}
            onChange={(e) => handleChange('address', e.target.value)}
            className="w-full px-4 py-2 rounded-lg border border-[#E8E8E8] bg-white text-[#131313] text-base placeholder:text-[#64748B] placeholder:text-xs placeholder: focus:outline-none focus:ring-2"
          />          
          <DatePickerField
            label="Reported Date"
            labelClassName="block mb-2 text-[#131313] text-base  "
            placeholder="Please Select Reported Date"
            value={formData.reportedDate}
            onChange={(date) => handleChange('reportedDate', date)}
            className="flex justify-center items-start p-2 w-full rounded-lg border border-[#E8E8E8] bg-white text-[#131313] text-base placeholder:text-[#64748B] placeholder:text-xs focus:outline-none focus:ring-2"
          />
        </div>
      )}
    </div>
  );
};

export default NewAnomalyRegistration;
