import React, { useState } from 'react'
import DatePickerField from './forms/DatePickerField'
import SelectField from './forms/SelectField'

const CoordinateDetails = () => {
  // Mock data - replace with API data later
  const [formData, setFormData] = useState({
    // Non-editable fields (from API)
    coordinates: '35.575990880907O3,140.1625671213708',
    detectionType: 'Manual Registration',
    incidentNumber: '4702',
    reportedDateDisplay: '09/05/2024 05:35 AM',
    direction: 'North-East',
    anomalyLocationCoordinates: '35.575990880907O3,140.1625671213708',

    // Editable fields - pre-filled with API response
    severityType: { value: 'Medium', label: 'Medium' },
    workRequestStatus: { value: 'Requested', label: 'Requested' },
    workProgressStatus: { value: 'Emergency Repair', label: 'Emergency Repair' },
    incidentType: { value: 'Fallen Tree Survey', label: 'Fallen Tree Survey' },
    latestUpdateDate: '09/05/2024',
    comment: '' // Only comment field is empty initially
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Define select field styles
  const selectFieldStylesBlue = {
    control: (base) => ({
      ...base,
      backgroundColor: '#EEF4FA',
      border: 'none',
      borderColor: 'transparent',
      boxShadow: 'none',
      '&:hover': { borderColor: 'transparent', border: 'none' },
      '&:focus': { borderColor: 'transparent', boxShadow: 'none', outline: 'none', border: 'none' }
    }),
    indicatorSeparator: () => ({
      display: 'none'
    }),
    dropdownIndicator: (base) => ({
      ...base,
      color: '#000000'
    })
  }

  const selectFieldStylesWhite = {
    control: (base) => ({
      ...base,
      backgroundColor: '#FFFFFF',
      border: 'none',
      borderColor: 'transparent',
      boxShadow: 'none',
      '&:hover': { borderColor: 'transparent', border: 'none' },
      '&:focus': { borderColor: 'transparent', boxShadow: 'none', outline: 'none', border: 'none' }
    }),
    indicatorSeparator: () => ({
      display: 'none'
    }),
    dropdownIndicator: (base) => ({
      ...base,
      color: '#000000'
    })
  }

  const severityOptions = [
    { value: 'Low', label: 'Low' },
    { value: 'Medium', label: 'Medium' },
    { value: 'High', label: 'High' },
    { value: 'Critical', label: 'Critical' }
  ]

  const workRequestStatusOptions = [
    { value: 'Requested', label: 'Requested' },
    { value: 'In Progress', label: 'In Progress' },
    { value: 'Completed', label: 'Completed' },
    { value: 'Cancelled', label: 'Cancelled' }
  ]

  const workProgressStatusOptions = [
    { value: 'Emergency Repair', label: 'Emergency Repair' },
    { value: 'Routine Maintenance', label: 'Routine Maintenance' },
    { value: 'Scheduled Repair', label: 'Scheduled Repair' },
    { value: 'Inspection', label: 'Inspection' }
  ]

  const incidentTypeOptions = [
    { value: 'Fallen Tree Survey', label: 'Fallen Tree Survey' },
    { value: 'Road Damage', label: 'Road Damage' },
    { value: 'Infrastructure Issue', label: 'Infrastructure Issue' },
    { value: 'Safety Hazard', label: 'Safety Hazard' }
  ]

  return (
    <div className='border border-[#E5E7EB] rounded-xl mt-3 overflow-hidden'>
      <div className='space-y-0'>
        {/* Non-editable Fields */}

        {/* Coordinates */}
        <div className='flex  items-center bg-[#EEF4FA] border-b border-[#E5E7EB]'>
          <div className='w-48 px-4 py-3 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Coordinates</label>
          </div>
          <div className='flex-1 px-2 py-3'>
            <span className='text-sm text-gray-900 font-medium text-nowrap'>{formData.coordinates}</span>
          </div>
        </div>

        {/* Detection Type */}
        <div className='flex  items-center bg-[#FFFFFF] border-b border-[#E5E7EB]'>
          <div className='w-38 px-4 py-3'>
            <label className='text-sm font-medium'>Detection Type</label>
          </div>
          <div className='flex-1 px-2 py-3'>
            <span className='text-sm text-gray-900 font-medium'>{formData.detectionType}</span>
          </div>
        </div>

        {/* Severity Type - Editable */}
        <div className='flex  items-center bg-[#EEF4FA] border-b border-[#E5E7EB]'>
          <div className='w-38 px-4 py-3 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Severity Type</label>
          </div>
          <div className='flex-1 px-0 py-3'>
            <div className='w-auto'>
              <SelectField
                value={formData.severityType}
                onChange={(value) => handleInputChange('severityType', value)}
                options={severityOptions}
                placeholder="Select..."
                className="text-sm"
                styles={selectFieldStylesBlue}
              />
            </div>
          </div>
        </div>

        {/* Work Request Status - Editable */}
        <div className='flex  items-center bg-[#FFFFFF] border-b border-[#E5E7EB]'>
          <div className='w-38 px-4 py-3 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Work Request Status</label>
          </div>
          <div className='flex-1 px-0 py-3'>
            <div className=''>
              <SelectField
                value={formData.workRequestStatus}
                onChange={(value) => handleInputChange('workRequestStatus', value)}
                options={workRequestStatusOptions}
                placeholder="Select..."
                className="text-sm"
                styles={selectFieldStylesWhite}
              />
            </div>
          </div>
        </div>

        {/* Work Progress Status - Editable */}
        <div className='flex  items-center bg-[#EEF4FA] border-b border-[#E5E7EB]'>
          <div className='w-38 px-4 py-3 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Work Progress Status</label>
          </div>
          <div className='flex-1 px-0 py-3'>
            <div className=''>
              <SelectField
                value={formData.workProgressStatus}
                onChange={(value) => handleInputChange('workProgressStatus', value)}
                options={workProgressStatusOptions}
                placeholder="Select..."
                className="text-sm"
                styles={selectFieldStylesBlue}
              />
            </div>
          </div>
        </div>

        {/* Incident Type - Editable */}
        <div className='flex  items-center bg-[#FFFFFF] border-b border-[#E5E7EB]'>
          <div className='w-38 px-4 py-3 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Incident Type</label>
          </div>
          <div className='flex-1 px-0 py-3'>
            <div className=''>
              <SelectField
                value={formData.incidentType}
                onChange={(value) => handleInputChange('incidentType', value)}
                options={incidentTypeOptions}
                placeholder="Select..."
                className="text-sm"
                styles={selectFieldStylesWhite}
              />
            </div>
          </div>
        </div>
        <div className='flex  items-center bg-[#EEF4FA]'>
          <div className='w-38 px-4 py-1 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Reported Date Display</label>
          </div>
          <div className='flex-1 px-0 py-1'>
            <div className=''>
              <DatePickerField
                value={formData.reportedDateDisplay}
                className="text-sm !px-2.5 py-2 rounded-lg border-0 focus:outline-none focus:ring-0 !bg-[#EEF4FA]"
              />
            </div>
          </div>
        </div>

        {/* Latest Update Date - Editable */}
        <div className='flex  items-center bg-[#FFFFFF] border-b border-[#E5E7EB]'>
          <div className='w-38 px-4 py-1 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Latest Update Date</label>
          </div>
          <div className='flex-1 px-0 py-1'>
            <div className=''>
              <DatePickerField
                value={formData.latestUpdateDate}
                onChange={(value) => handleInputChange('latestUpdateDate', value)}
                className="text-sm !px-2.5 !pr-0 py-1 rounded-lg border-0 focus:outline-none focus:ring-0"
                style={{ backgroundColor: '#FFFFFF' }}
              />
            </div>
          </div>
        </div>

        {/* Incident Number - Non-editable */}
        <div className='flex items-center bg-[#EEF4FA] border-b border-[#E5E7EB]'>
          <div className='w-38 px-4 py-1 border-r border-[#FFF]'>
            <label className='text-sm font-medium'>Incident Number</label>
          </div>
          <div className='flex-1 px-2.5 py-1'>
            <span className='text-sm text-gray-900 font-medium'>{formData.incidentNumber}</span>
          </div>
        </div>

        {/* Comment - Editable */}
        <div className='flex  items-center bg-[#FFFFFF]'>
          <div className='w-38 px-4 py-1 border-r border-[#FFF]'>
            <label className='text-sm font-medium pt-2'>Comment</label>
          </div>
          <div className='flex-1 px-1 py-1 flex items-center'>
            <textarea
              value={formData.comment}
              onChange={(e) => handleInputChange('comment', e.target.value)}
              placeholder="Enter your comments"
              className="w-full px-1 py-2 pb-0 pt-4 border-1 rounded-lg text-sm focus:outline-none  resize-none  placeholder:text-left"
              rows={2}
              style={{ backgroundColor: '#FFFFFF' }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default CoordinateDetails