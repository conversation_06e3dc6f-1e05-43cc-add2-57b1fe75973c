import { useState } from 'react'
import FilterIcon from '../../assets/icons/filterIcon.svg'
import refreshIcon from '../../assets/icons/refreshIcon.svg'
import CustomCheckbox from './CustomCheckbox'
import ImageIcon from '../../assets/icons/imageRecognition.svg'
import sensorIcon from '../../assets/icons/sensorIcon.svg'
import manualIcon from '../../assets/icons/manualcon.svg'
import lowSeverity from '../../assets/icons/lowSeverity.svg'
import mediumSeverity from '../../assets/icons/mediumSeverity.svg'
import highSeverity from '../../assets/icons/highSeverity.svg'

const FilterComponent = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedDetection, setSelectedDetection] = useState([]);
  const [selectedSeverity, setSelectedSeverity] = useState([]);
  const [selectedStatus, setSelectedStatus] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const checkboxStyle = "appearance-none h-4 w-4 border border-gray-300 rounded relative cursor-pointer checked:border-gray-300 focus:outline-none after:content-['✓'] after:hidden after:text-black after:absolute after:text-xs after:top-[-1px] after:left-[3px] checked:after:block checked:bg-white";

  const handleCheckboxChange = (category, value) => {
    switch(category) {
      case 'detection':
        setSelectedDetection(prev => 
          prev.includes(value) 
            ? prev.filter(item => item !== value)
            : [...prev, value]
        );
        break;
      case 'severity':
        setSelectedSeverity(prev => 
          prev.includes(value) 
            ? prev.filter(item => item !== value)
            : [...prev, value]
        );
        break;
      case 'status':
        setSelectedStatus(prev => 
          prev.includes(value) 
            ? prev.filter(item => item !== value)
            : [...prev, value]
        );
        break;
    }
  };

  const handleReset = () => {
    setSelectedDetection([]);
    setSelectedSeverity([]);
    setSelectedStatus([]);
    console.log('Filters reset');
  };

  const handleApplyFilters = () => {
    const filterData = {
      detectionMethods: selectedDetection,
      severityTypes: selectedSeverity,
      status: selectedStatus
    };

    console.log('Selected Filters:', filterData);
    setIsExpanded(false);
  };

  return (
    <div className="relative">
      <div 
        className='w-[396px] h-[49px] flex-shrink-0 rounded-xl bg-white/90 backdrop-blur-[2px] flex justify-between items-center px-4 cursor-pointer'
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className='text-[#042174] font-["Nunito_Sans"] text-base font-semibold'>Select filter options here</span>
        <img src={FilterIcon} alt="Filter"/>
      </div>

      {isExpanded && (
        <div className="absolute top-full left-0 mt-2 w-[396px] bg-white rounded-xl p-6 shadow-lg z-10">
          <div className="space-y-6">
            {/* Detection Methods */}
            <div>
              <h3 className="text-[#131313] text-xs  mb-3">Detection Methods</h3>
              <div className="space-y-3">
                <CustomCheckbox
                  label="Image Recognition Detection"
                  checked={selectedDetection.includes('image')}
                  onChange={() => handleCheckboxChange('detection', 'image')}
                  name="detection"
                  value="image"
                  className={checkboxStyle}
                  icon={ImageIcon}
                />
                <CustomCheckbox
                  label="Sensor Detection"
                  checked={selectedDetection.includes('sensor')}
                  onChange={() => handleCheckboxChange('detection', 'sensor')}
                  name="detection"
                  value="sensor"
                  className={checkboxStyle}
                  icon={sensorIcon}
                />
                <CustomCheckbox
                  label="Manual Registration"
                  checked={selectedDetection.includes('manual')}
                  onChange={() => handleCheckboxChange('detection', 'manual')}
                  name="detection"
                  value="manual"
                  className={checkboxStyle}
                  icon={manualIcon}
                />
              </div>
            </div>

            {/* Severity Type */}
            <div>
              <h3 className="text-[#131313] text-xs  mb-3">Severity Type</h3>
              <div className="space-y-3">
                <CustomCheckbox
                  label="Low"
                  checked={selectedSeverity.includes('low')}
                  onChange={() => handleCheckboxChange('severity', 'low')}
                  name="severity"
                  value="low"
                  className={checkboxStyle}
                  icon={lowSeverity}
                />
                <CustomCheckbox
                  label="Medium"
                  checked={selectedSeverity.includes('medium')}
                  onChange={() => handleCheckboxChange('severity', 'medium')}
                  name="severity"
                  value="medium"
                  className={checkboxStyle}
                  icon={mediumSeverity}
                />
                <CustomCheckbox
                  label="High"
                  checked={selectedSeverity.includes('high')}
                  onChange={() => handleCheckboxChange('severity', 'high')}
                  name="severity"
                  value="high"
                  className={checkboxStyle}
                  icon={highSeverity}
                />
              </div>
            </div>

            {/* Status */}
            <div>
              <h3 className="text-[#131313] text-xs  mb-3">Status</h3>
              <div className="space-y-3">
                <CustomCheckbox
                  label="Not Set"
                  checked={selectedStatus.includes('not_set')}
                  onChange={() => handleCheckboxChange('status', 'not_set')}
                  name="status"
                  value="not_set"
                  className={checkboxStyle}
                />
                <CustomCheckbox
                  label="Requested"
                  checked={selectedStatus.includes('requested')}
                  onChange={() => handleCheckboxChange('status', 'requested')}
                  name="status"
                  value="requested"
                  className={checkboxStyle}
                />
                <CustomCheckbox
                  label="No Requested Needed"
                  checked={selectedStatus.includes('no_request_needed')}
                  onChange={() => handleCheckboxChange('status', 'no_request_needed')}
                  name="status"
                  value="no_request_needed"
                  className={checkboxStyle}
                />
                <CustomCheckbox
                  label="Work Completed"
                  checked={selectedStatus.includes('work_completed')}
                  onChange={() => handleCheckboxChange('status', 'work_completed')}
                  name="status"
                  value="work_completed"
                  className={checkboxStyle}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div>
              <div className="flex justify-end items-center gap-4">
                <button 
                  onClick={handleReset}
                  className="p-2 hover:opacity-80 transition-opacity"
                >
                  <img src={refreshIcon} className='w-6 h-6' alt="Reset filters"/>
                </button>
                <button 
                  className="bg-[#042174] text-white px-6 py-2 rounded-lg text-sm font-medium disabled:opacity-50 hover:opacity-90 transition-opacity"
                  onClick={handleApplyFilters}
                  disabled={isLoading || (selectedDetection.length === 0 && selectedSeverity.length === 0 && selectedStatus.length === 0)}
                >
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FilterComponent