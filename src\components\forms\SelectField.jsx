import React from 'react';
import Select from 'react-select';

const SelectField = ({
  label,
  name,
  options,
  value,
  onChange,
  error,
  isMulti = false,
  isSearchable = true,
  placeholder = 'Select...',
  className = '',
  labelClassName = '',
  required = false,
  disabled = false,
  inputWrapperClassName="",
  
  ...props
}) => {

  const customStyles = {
    control: (base, state) => ({
      ...base,
      borderColor: error ? '#ef4444' : state.isFocused ? '#3b82f6' : '#d1d5db',
      boxShadow: 'none',
      '&:hover': {
        borderColor: state.isFocused ? '#3b82f6' : '#d1d5db'
      },
      borderRadius: '0.75rem',
      padding: '2px',
      minHeight: '34px',
      backgroundColor: 'white',
      cursor: 'pointer'
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected 
        ? '#3b82f6' 
        : state.isFocused 
          ? '#e5e7eb' 
          : 'transparent',
      color: state.isSelected ? 'white' : '#131313',
      fontSize: '16px',
      fontFamily: 'Nunito Sans, sans-serif',
      cursor: 'pointer',
      '&:active': {
        backgroundColor: '#3b82f6'
      }
    }),
    placeholder: (base) => ({
      ...base,
      color: '#64748B',
      fontSize: '12px',
      fontWeight: 400,
      fontFamily: 'Nunito Sans, sans-serif'
    }),
    singleValue: (base) => ({
      ...base,
      color: '#131313',
      fontSize: '16px',
      fontWeight: 400,
      fontFamily: 'Nunito Sans, sans-serif'
    }),
    indicatorSeparator: () => ({
      display: 'none'
    }),
    dropdownIndicator: (base) => ({
      ...base,
      padding: '0 8px',
      color: '#131313',
      '&:hover': {
        color: '#3b82f6'
      }
    }),
    menu: (base) => ({
      ...base,
      borderRadius: '0.75rem',
      overflow: 'hidden',
      backgroundColor: 'white',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
    })
  };

  return (
    <div className={`"form-field" ${inputWrapperClassName}`}>
      {label && (
        <label 
          htmlFor={name} 
          className={`block mb-2 text-base ${labelClassName}`}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <Select
        id={name}
        name={name}
        options={options}
        value={value}
        onChange={onChange}
        isMulti={isMulti}
        isSearchable={isSearchable}
        placeholder={placeholder}
        isDisabled={disabled}
        className={`${className} react-select-container`}
        classNamePrefix="react-select"
        styles={customStyles}
        {...props}
      />
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};

export default SelectField;
