import { useState } from "react";
import downArrow from "../assets/icons/downArrow.svg";
import license from '../assets/icons/License.svg'
import ContactInformation from "../components/ContactInformation";

const License = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };


  return (
    <div className="flex flex-col gap-2 mb-3">
      <div
        className="flex items-center p-[8px] w-[318px] rounded-xl border border-[rgba(18,18,18,0.08)] bg-[#EEF4FA] cursor-pointer"
        onClick={handleToggle}
      >
        <div className="flex items-center gap-2">
          <img src={license} alt="search" className="w-5 h-5" />
          <span className="text-[#042174] text-base font-medium">License</span>
        </div>
        <div className="ml-auto">
          <img
            src={downArrow}
            alt="expand"
            className={`w-5 h-5 transition-transform duration-200 ${
              isExpanded ? "rotate-180" : ""
            }`}
          />
        </div>
      </div>

      {isExpanded && (
        <div className="flex gap-2 w-[318px] rounded-xl border border-[#E1E1E2] bg-white p-2">
          <ContactInformation/>
        </div>
      )}
    </div>
  );
};

export default License;
