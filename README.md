
1.	Folder Structure:
  src/
├── assets/         # Images, Icons, fonts
├── components/     # Reusable components
├── hooks/          # Custom React hooks
├── layouts/        # Page/layout wrappers, sidebar
├── pages/          # Page components 
├── routes/         # React Router configuration
├── utils/          # Reusable functions
├── App.jsx
├── main.jsx




2.	 Recommended Libraries

Purpose		                Library

Routing		                react-router-dom
Forms		                  react-hook-form
State Management	        React Context or redux-toolkit
API Requests	            axios
CSS Framework	            Tailwindcss
<!-- Testing		                vitest, react-testing-library -->
Linting/Formatting	      eslint, prettier


3.	Use a Naming Convention Style (Consistently): 

Case Style	Used For	Example
camelCase	Variables, functions, props	userName, handleClick
PascalCase	Components, classes, interfaces	UserCard, AuthService
kebab-case	File names, URLs (not variables)	user-profile, reset-password
UPPER_SNAKE_CASE	Constants	API_BASE_URL, MAX_RETRIES


4. Recommended Storage:

Authentication Tokens (Access Token)             ⚠️ Avoid localStorage — prefer cookies (HttpOnly + Secure)
                                sessionStorage 


<!-- Normal Cookies :  -->
A)Long-lived State    

Normal Cookie Example:
---------------------
document.cookie = "theme=dark";
const cookies = document.cookie; // Accessible from JS     

==================================================================================================

<!-- HttpOnly Cookies : -->
a)Authentication Tokens (Access Token) 

| Use Case               | Use HttpOnly? |
| ---------------------- | ------------- |
| Access Tokens (JWT)    | ✅ Yes         |
| Refresh Tokens         | ✅ Yes         |
| Session IDs            | ✅ Yes         |



<!-- HttpOnly Cookie Example: -->
Set-Cookie: token=abc123; HttpOnly; Secure; SameSite=Strict

==================================================================================================

<!-- LocalStorage : -->

| Use Case               | localstorage  |
| ---------------------- | ------------- |
| Theme / UI Settings    | ✅ Yes        |
| User Prefs             | ✅ Yes        |
| isLoggedIn             | ✅ Yes        |
| sidebarCollapsed       | ✅ Yes        |
| activeTab              | ✅ Yes        |
| showPromoPopup         | ✅ Yes        |
| showNotification       | ✅ Yes        |
 
==================================================================================================

Highly sensitive data (passwords)                ❌ Never store on client-side storage

==================================================================================================

<!-- SessionStorage or in-memory (useState, context) : -->
a) Temporary form data (non-auth)                   
b) Short-lived State
c) Temporary state (e.g., form data, filters)       



==================================================================================================

5. General Naming Rules:
------------------------
 🔤 Be descriptive: Avoid single-letter names (a, x); prefer userId, imageUrl, etc.

 ✅ Boolean variables: Use prefixes like is, has, can, should
•	isLoading, hasAccess, canEdit

 📜 Function names: Use verbs
•	getUserData(), handleSubmit(), fetchCourses()

 🧱 Arrays: Use plural
•	users, products, courses

 📁 Component names: PascalCase
•	UserProfile, CourseCard
==================================================================================================

6.	 Team Guidelines / README.md: 
🔠 All variables and functions must use camelCase.
🔤 All components and class names must use PascalCase.
🔒 Constants are written in UPPER_SNAKE_CASE.
📁 Files are named in kebab-case.
Example :
const userName = 'John'; // ✅ camelCase (variable)
const API_URL = 'https://example.com'; // ✅ UPPER_SNAKE_CASE (constant)
const isLoggedIn = true; // ✅ boolean with is/has prefix

===========================================================================
function fetchUserData() {
  // ✅ function name starts with a verb
  
}

1. Data Fetching / API Calls

| Purpose     | Function Name Examples              |
| ----------- | ----------------------------------- |
| Get data    | `fetchData`, `getUser`, `loadPosts` |
| Send data   | `submitForm`, `postComment`         |
| Update data | `updateUser`, `editProfile`         |
| Delete data | `deleteRecord`, `removeItem`        |


2. UI Events / Actions

| Purpose             | Function Name Examples                        |
| ------------------- | --------------------------------------------- |
| Handle UI changes   | `handleClick`, `handleChange`, `handleSubmit` |
| Toggle UI           | `toggleSidebar`, `toggleModal`                |
| Open/Close elements | `openMenu`, `closeDialog`                     |
| Show/Hide           | `showAlert`, `hideTooltip`                    |


3. State Management / Logic

| Purpose             | Function Name Examples                 |
| ------------------- | -------------------------------------- |
| Set values          | `setUserData`, `setTheme`              |
| Reset/Clear         | `resetForm`, `clearFilters`            |
| Increment/Decrement | `incrementCounter`, `decreaseQuantity` |

4. Validation & Checking

| Purpose      | Function Name Examples                       |
| ------------ | -------------------------------------------- |
| Check/Verify | `validateForm`, `checkToken`                 |
| Compare      | `comparePasswords`, `matchValues`            |


5. Navigation / Routing

| Purpose      | Function Name Examples                       |
| ------------ | -------------------------------------------- |
| Navigate     | `navigateTo`, `redirectTo`, `goBack`          |
| Route changes| `changeRoute`, `updatePath`                  |


===========================================================================