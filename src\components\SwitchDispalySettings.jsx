import { useState, useEffect } from "react";
import downArrow from "../assets/icons/downArrow.svg";
import settingsIcon from "../assets/icons/SettingsIcon.svg";
import { InputField, SelectField, DatePickerField } from "../components/forms";
import lowSeverity from "../assets/icons/lowSeverity.svg";
import mediumSeverity from "../assets/icons/mediumSeverity.svg";
import highSeverity from "../assets/icons/highSeverity.svg";
import ImageIcon from "../assets/icons/imageRecognition.svg";
import sensorIcon from "../assets/icons/sensorIcon.svg";
import manualIcon from "../assets/icons/manualcon.svg";

const SwitchDispalySettings = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [apiData, setApiData] = useState({
    discoverTypeOptions: [],
    severityTypeOptions: [],
    currentIssueStatusOptions: [],
    incidentTypeOptions: [],
    workProgressStatusOptions: [],
    imageDataAvailabilityOptions: [],
  });

  const [formData, setFormData] = useState({
    reportedDate: "",
    latestUpdateDate: "",
    imageUpdateDate: "",
    address: "",
    discoverType: "",
    severityType: "",
    currentIssueStatus: "",
    incidentType: "",
    workProgressStatus: "",
    imageDataAvailability: "",
  });

  // Fetch options from API
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        setIsLoading(true);
        // Replace these with your actual API endpoints
        const [
          discoverTypeResponse,
          severityTypeResponse,
          currentIssueStatusResponse,
          incidentTypeResponse,
          workProgressStatusResponse,
          imageDataAvailabilityResponse,
        ] = await Promise.all([
          fetch("/api/discover-types"),
          fetch("/api/severity-types"),
          fetch("/api/current-issue-statuses"),
          fetch("/api/incident-types"),
          fetch("/api/work-progress-statuses"),
          fetch("/api/image-data-availability"),
        ]);

        const [
          statusOptions,
          severityOptions,
          priorityOptions,
          categoryOptions,
          assigneeOptions,
          locationOptions,
        ] = await Promise.all([
          discoverTypeResponse.json(),
          severityTypeResponse.json(),
          currentIssueStatusResponse.json(),
          incidentTypeResponse.json(),
          workProgressStatusResponse.json(),
          imageDataAvailabilityResponse.json(),
        ]);
        setApiData({
          discoverTypeOptions: statusOptions,
          severityTypeOptions: severityOptions,
          currentIssueStatusOptions: priorityOptions,
          incidentTypeOptions: categoryOptions,
          workProgressStatusOptions: assigneeOptions,
          imageDataAvailabilityOptions: locationOptions,
        });
      } catch (error) {
        console.error("Error fetching options:", error);
        // You might want to show an error message to the user
      } finally {
        setIsLoading(false);
      }
    };

    if (isExpanded) {
      fetchOptions();
    }
  }, [isExpanded]); // Only fetch when the component is expanded

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }; // Define select fields configuration with dynamic options from API
  const selectFieldsConfig = [
    {
      id: "discoverType",
      label: "Discover Type",
      placeholder: "Please Select Discover Type",
      options: [
        {
          value: "imageRecognition",
          label: "Image Recognition Detection",
          icon: ImageIcon,
        },
        {
          value: "sensorDetection",
          label: "Sensor Detection",
          icon: sensorIcon,
        },
        {
          value: "manualRegistration",
          label: "Manual Registration",
          icon: manualIcon,
        },
      ],
      isMulti: true,
      showSelectAll: true,
      showCheckbox: true,
      customOptionRender: true,
    },
    {
      id: "severityType",
      label: "Severity Type",
      placeholder: "Please Select Severity Type",
      options: [
        { value: "low", label: "Low", icon: lowSeverity },
        { value: "medium", label: "Medium", icon: mediumSeverity },
        { value: "high", label: "High", icon: highSeverity },
      ],
      customOptionRender: true,
    },
    {
      id: "currentIssueStatus",
      label: "Current Issue Status",
      placeholder: "Please Select Current Issue Status",
      options: [
        { value: "unaddressed", label: "1. Unaddressed" },
        { value: "emergencyRepair", label: "2. Emergency Repair" },
        { value: "onHold", label: "3. On Hold (Monitoring)" },
        { value: "contactPerson", label: "4. Contact Person in Charge" },
        { value: "contactTeam", label: "5. Contact Other Team" },
        {
          value: "outsideJurisdiction",
          label: "6. Anomaly Outside Jurisdiction",
        },
        { value: "notFound", label: "7. Anomaly Not Found" },
        { value: "changestoday", label: "Anomalies Changes Today" },
      ],
      isMulti: true,
      showSelectAll: true,
      showCheckbox: true,
    },
    {
      id: "incidentType",
      label: "Incident Type",
      placeholder: "Please Select Incident Type",
      options: [
        { value: "pothole", label: "1. Pothole" },
        { value: "fallenTree", label: "2. Fallen Tree" },
        { value: "waterlogging", label: "3. Waterlogging" },
        { value: "landslide", label: "4. Landslide" },
        { value: "roadDamageCracks", label: "5. Road Damage/Cracks" },
        { value: "brokenDrainCover", label: "6. Broken Drain Cover" },
        { value: "blockedDrain", label: "7. Blocked Drain" },
        { value: "others", label: "8. Others" },
      ],
      isMulti: true,
      showSelectAll: true,
      showCheckbox: true,
    },
    {
      id: "workProgressStatus",
      label: "Work Progress Status",
      placeholder: "Please Select Work Progress Status",
      options: [
        { value: "notSet", label: "1. Not Set" },
        { value: "requested", label: "2. Requested" },
        { value: "noRequestedNeeded", label: "3. No Requested Needed" },
        { value: "workCompleted", label: "4. Work Completed" },
      ],
      isMulti: true,
      showSelectAll: true,
      showCheckbox: true,
    },
    {
      id: "imageDataAvailability",
      label: "Image Data Availability",
      placeholder: "Please Select Image Data Availability",
      options: [
        { value: "none", label: "None" },
        { value: "smartphone", label: "Yes (photo taken with smartphone)" },
        {
          value: "carDevice",
          label: "Yes (photo taken by car onboard device)",
        },
      ],
      isMulti: true,
      showSelectAll: true,
      showCheckbox: true,
    },
  ];

  return (
    <div className="flex flex-col gap-2 mb-3">
      <div
        className="flex items-center p-[8px] w-[318px] rounded-xl border border-[rgba(18,18,18,0.08)] bg-[#EEF4FA] cursor-pointer"
        onClick={handleToggle}
      >
        <div className="flex items-center gap-2">
          <img src={settingsIcon} alt="search" className="w-5 h-5" />
          <span className="text-[#042174] text-base font-medium">
            Switch Display settings
          </span>
        </div>
        <div className="ml-auto">
          <img
            src={downArrow}
            alt="expand"
            className={`w-5 h-5 transition-transform duration-200 ${
              isExpanded ? "rotate-180" : ""
            }`}
          />
        </div>
      </div>

      {isExpanded && (
        <div className="flex flex-col gap-4 w-[318px] rounded-xl border border-[#E1E1E2] bg-white p-4">
          <DatePickerField
            label="Reported Date"
            labelClassName="block mb-2 text-[#131313] text-base  "
            placeholder="Please Select Reported Date"
            value={formData.reportedDate}
            onChange={(value) => handleChange("reportedDate", value)}
            className="flex justify-center items-start p-2 w-full rounded-lg border border-[#E8E8E8] bg-white text-[#131313] text-base placeholder:text-[#64748B] placeholder:text-xs focus:outline-none focus:ring-2"
          />
          <DatePickerField
            label="Latest Update Date"
            labelClassName="block mb-2 text-[#131313] text-base  "
            placeholder="Please Select Latest Update Date"
            value={formData.latestUpdateDate}
            onChange={(value) => handleChange("latestUpdateDate", value)}
            className="flex justify-center items-start p-2 w-full rounded-lg border border-[#E8E8E8] bg-white text-[#131313] text-base placeholder:text-[#64748B] placeholder:text-xs focus:outline-none focus:ring-2"
          />
          <DatePickerField
            label="Image Update Date"
            labelClassName="block mb-2 text-[#131313] text-base  "
            placeholder="Please Select Image Update Date"
            value={formData.imageUpdateDate}
            onChange={(value) => handleChange("imageUpdateDate", value)}
            className="flex justify-center items-start p-2 w-full rounded-lg border border-[#E8E8E8] bg-white text-[#131313] text-base placeholder:text-[#64748B] placeholder:text-xs focus:outline-none focus:ring-2"
          />
          <InputField
            label="Address"
            labelClassName="!text-[#131313] !text-[16px]  block mb-2 "
            placeholder="Please Enter Address"
            value={formData.address}
            onChange={(e) => handleChange("address", e.target.value)}
            className="w-full px-4 py-2 rounded-lg border border-[#E8E8E8] bg-white text-[#131313] text-base placeholder:text-[#64748B] placeholder:text-xs placeholder: focus:outline-none focus:ring-2"
          />{" "}
          {selectFieldsConfig.map((field) => (
            <SelectField
              key={field.id}
              label={field.label}
              value={formData[field.id]}
              options={field.options}
              onChange={(value) => handleChange(field.id, value)}
              labelClassName="text-[#131313] text-base "
              isLoading={isLoading}
              placeholder={isLoading ? "Loading..." : field.placeholder}
              isMulti={field.isMulti}
              showSelectAll={field.showSelectAll}
              showCheckbox={field.showCheckbox}
              customOptionRender={field.customOptionRender}
              formatOptionLabel={
                field.customOptionRender
                  ? (option) => (
                      <div className="flex items-center gap-2">
                        <img
                          src={option.icon}
                          alt={option.label}
                          className="w-4 h-4"
                        />
                        <span>{option.label}</span>
                      </div>
                    )
                  : undefined
              }
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default SwitchDispalySettings;
