import React from 'react'

const CustomCheckbox = ({ 
  label, 
  checked, 
  onChange, 
  name, 
  value,
  className,
  icon=''
}) => {
  return (
    <label className="flex items-center space-x-2">
      <input 
        type="checkbox" 
        className={className}
        checked={checked}
        onChange={() => onChange(value)}
        name={name}
        value={value}
      />
      {icon && <img src={icon} alt="" className="w-4 h-4" />}
      <span className="text-[#131313] text-xs ">{label}</span>
    </label>
  )
}

export default CustomCheckbox
