import { useState, useEffect } from "react";
import aisinlogoWithSlogan from "../assets/icons/aisinlogoWithSlogan.png";
import expandIcon from "../assets/icons/expand.svg";
import menuBarIcon from "../assets/icons/menuBar.svg";
import SearchComponent from "../components/SearchComponent";
import SwitchDispalySettings from "../components/SwitchDispalySettings";
import License from "../components/License";
import NewAnomalyRegistration from "../components/NewAnomalyRegistration";

const Sidebar = ({ onToggle }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    onToggle?.(newExpandedState); // Call onToggle with new state if provided
  };

  // Initial state notification
  useEffect(() => {
    onToggle?.(isExpanded);
  }, []);

  return (
    <div
      className={`fixed left-0 top-0 flex flex-col 
        ${isExpanded ? "w-[346px] bg-white" : "w-[78px]"} 
        h-screen bg-[#042174] text-white z-[1000] 
        transition-all duration-600 ease-in-out transform`}
    >
      {/* Header with menu icon */}
      <div className="flex-none px-[10.579px] pt-[32.463px] pb-[18.463px]">
        <div className="w-full flex justify-center items-center">
          <img
            src={menuBarIcon}
            onClick={handleToggle}
            alt="Menu"
            className={`w-9 h-10 cursor-pointer transition-opacity duration-300 ${
              isExpanded ? "opacity-0 absolute" : "opacity-100"
            }`}
          />
        </div>
      </div>

      {/* Main content area with scroll */}
      <div
        className={`flex-1 overflow-y-auto px-[10.579px] transition-all duration-300 ease-in-out ${
          isExpanded ? "opacity-100" : "opacity-0 invisible"
        }`}
      >
        <div className="flex flex-col">
          <SearchComponent />
          <NewAnomalyRegistration />
          <SwitchDispalySettings />
          <License />
        </div>
      </div>

      {/* Footer with logo and buttons */}
      <div
        className={`flex-none px-[10.579px] pb-4 pt-4 border-t border-[#E1E1E2] bg-white flex flex-col items-center transition-all duration-300 ease-in-out ${
          isExpanded
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-4 invisible"
        }`}
      >
        <img
          src={aisinlogoWithSlogan}
          alt="Aisin Logo"
          className="w-auto h-auto mb-4"
        />
        <div className="flex items-center gap-3">
          <button className="flex px-3 text-nowrap py-2 justify-center items-center rounded-lg border border-[#001A72] bg-white text-[#001A72] hover:bg-gray-50 text-xs transition-colors duration-200">
            Michilog Support Page
          </button>
          <button className="flex w-[106px] h-[34px] px-3 py-2 justify-center items-center rounded-lg bg-[#001A72] text-white text-xs font-medium hover:bg-[#001A72]/90 transition-colors duration-200">
            Log Out
          </button>
        </div>
      </div>

      {/* Expand/Collapse Button */}
      {isExpanded && <button
        onClick={handleToggle}
        className="absolute top-5 -right-6 p-2 bg-[#042174] hover:bg-[#1976C2] rounded-[4px] h-[30.579px] flex items-center transition-colors duration-200"
      >
        <img
          src={expandIcon}
          alt="Expand"
          className={`h-5 w-5 transition-transform duration-300 ease-in-out ${
            isExpanded ? "rotate-180" : ""
          }`}
        />
      </button>}
    </div>
  );
};

export default Sidebar;
