@import "tailwindcss";

:root {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --aisin-blue-primary: #1265AE;
  --aisin-blue-secondary: #08599FCC;
  --aisin-green: #16A34A;
  --aisin-red: #FD7A88;
  --aisin-yellow: #FFD778;
  --aisin-purple: #C08BFF;
  --aisin-font-family: 'Manrope', sans-serif;
  --aisin-image-border-radius: 4px;
  
  --aisin-container-border-radius: 12px;
  --aisin-container-padding: 16px;
  --aisin-container-margin: 16px;
  --aisin-container-background-color: #fff;
  --aisin-container-border: 1px solid #E5E7EB;

  --aisin-container-title-font-size: 16px;
  --aisin-container-title-font-weight: 600;
  --aisin-container-title-font-color: #FFFFFF;
  --aisin-container-title-font-family: '<PERSON>unito Sans', sans-serif;
  --aisin-container-title-letter-spacing: 0%;

  --aisin-form-field-border-radius: 12px;
  --aisin-label-title-font-family: 'Nunito Sans', sans-serif;
  --aisin-label-font-size: 16px;
  --aisin-label-font-color: #131313;
  --aisin-label-font-weight: 400;

}
*{
  font-family: var(--aisin-font-family);
}
label{
  font-family: var(--aisin-label-title-font-family);
  font-size: var(--aisin-label-font-size);
  font-weight: var(--aisin-label-font-weight);
  letter-spacing: 0%;
  color: var(--aisin-label-font-color);
}
 

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
 
}
